from typing import Annotated, Any
from pathlib import Path
from pydantic import AfterValidator, Field
import os


def validate_no_path_traversal(v: Any) -> str:
    """Ensure no path traversal attacks."""
    if not isinstance(v, (str, Path)):
        raise ValueError("Path must be a string or Path object")

    path_str = str(v)

    if ".." in path_str:
        raise ValueError("Path traversal (..) not allowed")

    return path_str


def validate_absolute_path(v: str) -> str:
    """Ensure path is absolute."""
    path = Path(v)
    if not path.is_absolute():
        raise ValueError("Path must be absolute")
    return str(path)


def validate_input_file_exists(v: str) -> str:
    """Validate that input file exists and is accessible."""
    path = Path(v)

    if not path.exists():
        raise ValueError(f"Input file does not exist: {v}")

    if not path.is_file():
        raise ValueError(f"Path is not a file: {v}")

    # Check if file is readable
    if not os.access(path, os.R_OK):
        raise ValueError(f"Input file is not readable: {v}")

    return str(path)


def validate_directory_writable(v: str) -> str:
    """Validate that output directory exists and is writable."""
    path = Path(v)
    parent_dir = path.parent
    dir = path if path.is_dir() else path.parent

    # Create parent directories if they don't exist (optional behavior)
    # Uncomment the next two lines if you want auto-creation:
    # if not parent_dir.exists():
    #     parent_dir.mkdir(parents=True, exist_ok=True)

    if not dir.exists():
        raise ValueError(f"Output directory does not exist: {parent_dir}")

    if not os.access(dir, os.W_OK):
        raise ValueError(f"Output directory is not writable: {parent_dir}")

    return str(path)


def validate_reasonable_path_length(v: str) -> str:
    """Ensure path length is reasonable."""
    if len(v) > 4096:  # Most filesystems have path limits around 4096
        raise ValueError(f"Path too long: {len(v)} characters. Maximum: 4096")
    return v


def validate_file_is_writable(v: str) -> str:
    """Validate that output file is writable."""
    path = Path(v)
    if path.is_file() and not os.access(path, os.W_OK):
        raise ValueError(f"Output file is not writable: {v}")
    return str(path)


def input_path_post_validators(v: Any) -> str:
    v = validate_no_path_traversal(v)
    v = validate_absolute_path(v)
    v = validate_reasonable_path_length(v)
    v = validate_input_file_exists(v)
    return v


InputPath = Annotated[
    str,
    AfterValidator(input_path_post_validators),
    Field(
        description="Absolute path to input video file",
        examples=["/videos/input.mp4", "/mnt/storage/video.mkv"],
    ),
]


def output_path_post_validators(v: Any) -> str:
    v = validate_no_path_traversal(v)
    v = validate_absolute_path(v)
    v = validate_reasonable_path_length(v)
    v = validate_directory_writable(v)
    v = validate_file_is_writable(v)
    return v


OutputPath = Annotated[
    str,
    AfterValidator(output_path_post_validators),
    Field(
        description="Absolute path for output video file",
        examples=["/output/encoded.mp4", "/mnt/storage/output.ts"],
    ),
]
