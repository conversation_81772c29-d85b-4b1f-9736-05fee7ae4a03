from typing import List, Tuple, Optional
import os
import subprocess
from shlex import split
import logging

logger = logging.getLogger(__name__)


def command_to_args(cmd: str) -> list[str]:
    # Remove line continuations and normalize whitespace
    cmd_single_line = " ".join(cmd.splitlines()).replace("\\", "")
    return split(cmd_single_line)


class Process:
    def __init__(self, name: str, cmd: List[str] | str):
        self.name = name

        if isinstance(cmd, str):
            self.raw_cmd = cmd
            cmd = command_to_args(cmd)
        else:
            self.raw_cmd = " ".join(cmd)
        self.cmd = cmd

        self.next_process: Optional["Process"] = None
        self.prev_process: Optional["Process"] = None

        self.process: Optional[subprocess.Popen] = None

    def pipe(self, process: "Process"):
        self.next_process = process
        process.prev_process = self
        return self
    
    def run(self):
        if self.next_process:
            self.next_process.run()
        self._run()

    def run(self):
        if self.prev_process:
            assert (
                self.prev_process.process is not None
            ), "Previous process must be started before this one"
            input_fd = self.prev_process.process.stdout
        else:
            input_fd = None

        self.process = subprocess.Popen(
            self.cmd,
            stdin=input_fd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        if self.next_process:
            self.next_process.run()

        if (
            self.prev_process
            and self.prev_process.process
            and self.prev_process.process.stdout
        ):
            self.prev_process.process.stdout.close()

    def wait(self):
        res = self.process.wait()
        if res != 0:
            logger.error(f"Process {self.name} failed with exit code {res}")
            raise subprocess.CalledProcessError(
                res, " ".join(self.cmd), stderr=self.stderr()
            )

    def stdout(self) -> str:
        if self.process is None or self.process.stdout is None:
            raise ValueError("Process has not been started yet or does not have stdout")
        return self.process.stdout.read().decode("utf-8")

    def stderr(self) -> str:
        if self.process is None or self.process.stderr is None:
            raise ValueError("Process has not been started yet or does not have stderr")
        return self.process.stderr.read().decode("utf-8")

    def wait_all(self):
        if self.next_process:
            self.next_process.wait_all()
        self.wait()

    def debug(self):
        start_separator = "-" * 35 + " ERROR LOGS " + "-" * 35
        end_separator = "-" * 82
        logger.warning(
            f"Process {self.name}:\n{self.raw_cmd}\n{start_separator}\n{self.stderr()}\n{end_separator}"
        )
