{%- macro encoder_settings(quality, index) %}
    {%- set codec = quality.codec %}
    {%- set enc_type = quality.enc_type %}

    {%- if codec in ['libx264', 'libx265'] -%}
        {%- if enc_type == 'crf' -%}
            -crf {{ quality.crf }} -g:v:{{ index }} {{ quality.gop }} -keyint_min:v:{{ index }} {{ quality.gop }} -sc_threshold 0
        {%- elif enc_type == 'cbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -minrate:v:{{ index }} {{ quality.bitrate }} -maxrate:v:{{ index }} {{ quality.bitrate }} -bufsize:v:{{ index }} {{ (quality.bitrate * 2) }} -g:v:{{ index }} {{ quality.gop }} -keyint_min {{ quality.gop }} -sc_threshold 0
        {%- elif enc_type == 'vbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -maxrate:v:{{ index }} {{ quality.bitrate }} -g:v:{{ index }} {{ quality.gop }} -keyint_min:v:{{ index }} {{ quality.gop }} -sc_threshold 0
        {%- endif -%}
    {%- elif codec in ['h264_nvenc', 'hevc_nvenc', 'av1_nvenc'] -%}
        {%- if enc_type == 'crf' -%}
            -cq:v:{{ index }} {{ quality.crf }} -preset:v:{{ index }} p7 -g:v:{{ index }} {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- elif enc_type == 'cbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -preset:v:{{ index }} p7 -minrate:v:{{ index }} {{ quality.bitrate }} -maxrate:v:{{ index }} {{ quality.bitrate }} -bufsize:v:{{ index }} {{ (quality.bitrate * 2) }} -rc:v:{{ index }} cbr -g:v:{{ index }} {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- elif enc_type == 'vbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -preset:v:{{ index }} p7 -maxrate:v:{{ index }} {{ quality.bitrate * 1.2 }} -rc:v:{{ index }} vbr -g:v:{{ index }} {{ quality.gop }} -gpu {{ gpu | default('0') }}
        {%- endif -%}
    {%- elif codec in ['libsvtav1'] -%}
        {%- if enc_type == 'crf' -%}
            -crf {{ quality.crf }} -g:v:{{ index }} {{ quality.gop }}
        {%- elif enc_type == 'cbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -minrate:v:{{ index }} {{ quality.bitrate }} -maxrate:v:{{ index }} {{ quality.bitrate }} -bufsize:v:{{ index }} {{ (quality.bitrate * 2) }} -g:v:{{ index }} {{ quality.gop }}
        {%- elif enc_type == 'vbr' -%}
            -b:v:{{ index }} {{ quality.bitrate }} -maxrate:v:{{ index }} {{ quality.bitrate }} -g:v:{{ index }} {{ quality.gop }}
        {%- endif -%}
    {%- elif codec in ['aac', 'libopus', 'libmp3lame'] -%}
        -b:a {{ quality.bitrate }}
    {%- else -%}
        {# copy or unknown #}
    {%- endif -%}
{%- endmacro -%}


{%- macro filter_settings(quality, index) %}
    {%- if quality.codec != "copy" -%}
        {%- if quality.type == "video" -%}
            {%- if not quality.width and not quality.height -%}
                -filter:v:{{ index }} "fps={{ quality.fps }}"
            {%- else -%}
                -filter:v:{{ index }} "fps={{ quality.fps }},scale={{ quality.width or -1 }}:{{ quality.height or -1 }}"
            {%- endif -%}
        {%- elif quality.type == "audio" -%}
            -filter:a:{{ index }} "aresample=48000"
        {%- endif -%}
    {%- endif -%}
{%- endmacro -%}


ffmpeg -hide_banner -y -i {{ input_file | shellquote }} \
{%- for v_quality in qualities | selectattr("type", "equalto", "video") %}
    -map 0:v:{{ v_quality.source_index }} {{ filter_settings(v_quality, loop.index0) }} -c:v:{{ loop.index0 }} {{ v_quality.codec }} {{ encoder_settings(v_quality, loop.index0) }} \
{%- endfor %}
{%- for a_quality in qualities | selectattr("type", "equalto", "audio") %}
    -map 0:a:{{ a_quality.source_index }} {{ filter_settings(a_quality, loop.index0) }} -c:a:{{ loop.index0 }}  {{ a_quality.codec }} {{ encoder_settings(a_quality, loop.index0) }} \
{%- endfor %}
    -f mpegts -
