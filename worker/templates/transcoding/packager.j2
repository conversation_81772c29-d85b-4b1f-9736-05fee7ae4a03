{% macro var_stream_map(output) -%}
  -var_stream_map "
  {%- for index in output.source_indexes -%}
    {%- if index.startswith("v:") -%}
      {{ index }},agroup:audio,name:{{ output.streams[loop.index0] }}
    {%- elif index.startswith("a:") -%}
      {{ index }},agroup:audio,name:{{ output.streams[loop.index0] }}
    {%- endif %}
    {{- " " if not loop.last else "" -}}
  {%- endfor -%}
  "
{%- endmacro -%}

ffmpeg -hide_banner -y -f mpegts -i - \
{%- for output in outputs -%}
  {%- for index in output.source_indexes %}
    -map 0:{{ index }} \
  {%- endfor -%}
  {%- if output.type in  ["hls", "dash"] %}
    {{ var_stream_map(output) }} \
  {%- endif -%}
  {%- if output.type == "file" %}
    -c copy -f {{ output.format }} {{ output.file_name | shellquote }}
  {%- endif -%}
  {% if output.type == "hls" %}
    -c copy -f hls -hls_time {{ output.segment_duration }} -hls_playlist_type vod -master_pl_name {{ output.playlist_name | shellquote }} -hls_segment_filename {{ output.root_path | shellquote }}/%v/{{ output.segment_name | shellquote }}  {{ output.root_path | shellquote }}/%v/{{ output.subplaylist_name | shellquote }}
  {% endif %}
  {%- if not loop.last %} \{%- endif -%}
{%- endfor -%}
