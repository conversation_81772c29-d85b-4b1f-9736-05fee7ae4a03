__version__ = "0.1.0"


from celery import Celery
import os
import logging
from celery.app.log import TaskFormatter  # type: ignore
from celery.signals import after_setup_logger
import billiard

BROKER_URL = os.getenv("BROKER_URL") or "amqp://guest:guest@localhost:5672//"
RESULT_BACKEND = os.getenv("RESULT_BACKEND") or "rpc://"

app = Celery(
    "worker",
    broker=BROKER_URL,
    backend=RESULT_BACKEND,
)

app.conf.update(
    broker_connection_retry_on_startup=True,
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Europe/Paris",
    # enable events
    worker_send_task_events=True,
    task_send_sent_event=True,
    task_track_started=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_soft_time_limit=12 * 60 * 60,  # 12 hours soft limit
    task_time_limit=12 * 60 * 60 + 300,  # 12 hours + 5 minutes hard limit
)

app.autodiscover_tasks(["worker.tasks.transcoding_job"])

# ANSI color codes for log levels
LEVEL_COLORS = {
    "DEBUG": "\033[36m",  # Cyan
    "INFO": "\033[32m",  # Green
    "WARNING": "\033[33m",  # Yellow
    "ERROR": "\033[31m",  # Red
    "CRITICAL": "\033[41m",  # Red background
}
RESET = "\033[0m"


class WorkerIndexFilter(logging.Filter):
    def filter(self, record):
        try:
            record.worker_index = billiard.current_process().index
        except Exception:
            record.worker_index = "main"
        return True


class ColoredTaskFormatter(TaskFormatter):
    def format(self, record):
        levelname = record.levelname
        color = LEVEL_COLORS.get(levelname, "")
        record.levelname = f"{color}{levelname}{RESET}"
        return super().format(record)


@after_setup_logger.connect
def setup_loggers(logger, *args, **kwargs):
    formatter = ColoredTaskFormatter(
        "[%(levelname)s][%(asctime)s][%(name)s][id:%(worker_index)s][%(task_name)s][%(task_id)s] %(message)s"
    )

    # Add the worker index filter to all handlers
    for handler in logger.handlers:
        handler.addFilter(WorkerIndexFilter())
        handler.setFormatter(formatter)


# Set the logging level for specific loggers to avoid noise
for name in ["celery", "amqp", "kombu"]:
    logging.getLogger(name).setLevel(logging.WARNING)
