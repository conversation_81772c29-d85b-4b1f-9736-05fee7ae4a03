# syntax=docker/dockerfile:1
FROM jrottenberg/ffmpeg:6.1.2-nvidia2204

RUN apt update && \
    apt install python3 python3-pip -y && \
    rm -rf /var/lib/apt/lists/*

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache

WORKDIR /app

ARG UID=10001
RUN adduser \
    --disabled-password \
    --gecos "" \
    --home "/app" \
    --shell "/sbin/nologin" \
    --no-create-home \
    --uid "${UID}" \
    --ingroup www-data \
    apiuser && \
    pip install poetry && \
    poetry config virtualenvs.create false

COPY pyproject.toml poetry.lock ./

RUN --mount=type=cache,target=$POETRY_CACHE_DIR poetry install --only worker --no-root

# Switch to the non-privileged user to run the application.
USER apiuser

# Copy the source code into the container.
COPY . .

# Expose the port that the application listens on.
EXPOSE 8000

ENTRYPOINT [ "/bin/bash" ]

# Run the application.
CMD [ "/app/worker/init.sh" ]
