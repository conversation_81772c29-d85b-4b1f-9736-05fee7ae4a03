name: fastencoder

x-volumes:
  &default-volumes
  volumes:
    - "./videos/:/videos/:rw"

services:
  api:
    <<: *default-volumes
    image: registry-vor.hexaglobe.net/hexaglobe/fastencoder/api:${VERSION}
    depends_on:
      rabbitmq:
        condition: service_healthy
      result-db:
        condition: service_healthy
    env_file: ".env"
    ports:
      - "127.0.0.1:8000:8000"
    restart: unless-stopped

  rabbitmq:
    image: rabbitmq:4.0.4-management
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq/
      - rabbitmq-logs:/var/log/rabbitmq/
      - ./rabbitmq/20-fastencoder.conf:/etc/rabbitmq/conf.d/20-fastencoder.conf
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    ports:
      - "127.0.0.1:5672:5672"
      - "127.0.0.1:15672:15672"
    restart: unless-stopped

  result-db:
    image: postgres:14.17
    volumes:
      - "result-db:/var/lib/postgresql"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "127.0.0.1:5432:5432"
    restart: unless-stopped

  worker:
    <<: *default-volumes
    image: registry-vor.hexaglobe.net/hexaglobe/fastencoder/worker:${VERSION}
    # runtime: nvidia
    depends_on:
      rabbitmq:
        condition: service_healthy
      result-db:
        condition: service_healthy
    env_file: ".env"
    restart: unless-stopped

  streamprobe:
    <<: *default-volumes
    image: registry-vor.hexaglobe.net/hexaglobe/streamprobe:2.1.0-dev


volumes:
  rabbitmq-data:
    driver: local
  rabbitmq-logs:
    driver: local
  result-db:
    driver: local
