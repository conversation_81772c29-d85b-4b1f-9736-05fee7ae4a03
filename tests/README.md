# FastEncoder Tests

## Overview

This directory contains validation tests for the FastEncoder project, focusing on schema validation.

## Structure

- `validation/` - Schema and path validation tests (25 tests)
  - `test_schema_validation.py` - EncodeRequest schema validation (10 tests)
  - `test_path_validators.py` - Path validation functions (10 tests)
  - `test_api_validation.py` - API endpoint validation (5 tests)
- `fixtures/videos/` - Test video files
- `conftest.py` - Test configuration and fixtures

## Running Tests

### Install test dependencies

```bash
poetry install --group test
```

### Run all validation tests (25 tests)

```bash
# Using poetry (recommended)
poetry run pytest

# Or run specific validation tests
poetry run pytest tests/validation/
```

### Run specific test files

```bash
# Schema validation tests (10 tests)
poetry run pytest tests/validation/test_schema_validation.py

# Path validator tests (10 tests)
poetry run pytest tests/validation/test_path_validators.py

# API validation tests (5 tests)
poetry run pytest tests/validation/test_api_validation.py
```

### Run with markers and filters

```bash
# Run only validation tests
poetry run pytest -m validation

# Run specific validation scenarios
poetry run pytest -k "path_traversal"
poetry run pytest -k "invalid"

# Run with different output formats
poetry run pytest -v                    # verbose
poetry run pytest --tb=short           # short traceback
poetry run pytest -q                   # quiet
```

## Test Categories

### Schema Validation Tests
- Valid encode requests
- Invalid input/output paths
- Path traversal protection
- Quality validation
- Duplicate quality names
- Missing required fields

### Path Validation Tests
- Path traversal attempts
- Relative vs absolute paths
- File existence validation
- Pydantic integration

## Test Data

Test video files are located in `tests/fixtures/videos/`. The docker-compose configuration has been updated to mount this directory instead of the root `videos/` directory.
