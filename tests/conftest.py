"""Test configuration and fixtures."""

import pytest
import tempfile
from pathlib import Path


@pytest.fixture
def sample_video_file():
    """Path to a sample video file for testing."""
    video_file = Path("tests/fixtures/videos/bhakti.mp4").resolve()
    if not video_file.exists():
        pytest.skip(f"Test video file not found: {video_file}")
    return video_file


@pytest.fixture
def temp_output_dir():
    """Create a temporary directory for output files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)
